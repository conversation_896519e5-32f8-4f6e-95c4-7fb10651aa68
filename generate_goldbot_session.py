#!/usr/bin/env python3
"""
生成goldbot_session.session文件的认证脚本
用于ListingDataCollector
"""
import asyncio
from telethon import TelegramClient
from loguru import logger

# 从配置文件读取的Telegram配置
API_ID = 26145597
API_HASH = "859206f58db62ec957089a7e9ff11d38"
PHONE = "+8613375386798"
SESSION_NAME = "goldbot_session"

async def create_session():
    """创建Telegram session"""
    try:
        logger.info(f"开始创建 {SESSION_NAME}.session...")
        
        # 创建客户端，禁用代理
        client = TelegramClient(SESSION_NAME, API_ID, API_HASH, proxy=None)
        
        # 连接并认证
        print(f"使用电话号码: {PHONE}")
        print("将会发送验证码到你的Telegram...")
        await client.start(phone=PHONE)
        
        # 获取当前用户信息
        me = await client.get_me()
        logger.info(f"认证成功! 用户: {me.first_name} (@{me.username})")
        
        # 断开连接
        await client.disconnect()
        
        logger.info(f"✅ {SESSION_NAME}.session 创建成功!")
        
    except Exception as e:
        logger.error(f"❌ 创建session失败: {e}")
        raise

if __name__ == "__main__":
    logger.info("=" * 50) 
    logger.info("Goldbot Session 生成器")
    logger.info("用于 ListingDataCollector")
    logger.info("=" * 50)
    
    asyncio.run(create_session())